.page {
  position: relative;
  width: 1920px;
  height: 1964px;
  overflow: hidden;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 1920px;
  height: 1964px;
}

.group_1 {
  background-image: url(./img/15e8b85ed4654c07b1c1d7c677702da8_mergeImage.png);
  width: 1920px;
  height: 520px;
}

.block_1 {
  width: 1453px;
  height: 92px;
}

.group_2 {
  border-radius: 50%;
  background-image: url(./img/29d58bed8ff843b5a2cd3433009363bf_mergeImage.png);
  width: 42px;
  height: 42px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 23px;
}

.text_1 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin: 21px 0 0 12px;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  width: 1240px;
  height: 92px;
  margin-left: 41px;
}

.text_2 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 266px;
}

.text_3 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 39px;
}

.text_4 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_5 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_6 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.thumbnail_1 {
  width: 14px;
  height: 14px;
  margin: 39px 446px 0 40px;
}

.block_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 1478px;
  height: 92px;
  margin-left: 442px;
}

.text_7 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 50px;
}

.text_8 {
  width: 48px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 72px;
}

.text_9 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 70px;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 122px;
  margin: 0 447px 0 574px;
}

.text_10 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 33px 0 0 30px;
}

.text_11 {
  width: 192px;
  height: 48px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 48px;
  font-family: SourceHanSansCN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 72px;
  margin: 127px 0 0 1156px;
}

.text_12 {
  width: 308px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 7px 0 121px 1040px;
}

.group_4 {
  width: 1920px;
  height: 1445px;
  margin-bottom: 1px;
}

.text_13 {
  width: 871px;
  height: 66px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 33px;
  margin: 48px 0 0 527px;
}

.group_5 {
  width: 1161px;
  height: 46px;
  margin: 82px 0 0 359px;
}

.group_6 {
  background-color: rgba(255, 255, 255, 1);
  width: 94px;
  height: 46px;
  border: 1px solid rgba(0, 0, 0, 1);
}

.image-text_1 {
  width: 70px;
  height: 20px;
  margin: 12px 0 0 12px;
}

.text-group_1 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_2 {
  width: 10px;
  height: 6px;
  margin-top: 8px;
}

.group_7 {
  background-color: rgba(255, 255, 255, 1);
  width: 66px;
  height: 46px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 26px;
}

.image-text_2 {
  width: 42px;
  height: 20px;
  margin: 12px 0 0 12px;
}

.text-group_2 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_3 {
  width: 10px;
  height: 6px;
  margin-top: 8px;
}

.group_8 {
  background-color: rgba(255, 255, 255, 1);
  width: 66px;
  height: 46px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 26px;
}

.image-text_3 {
  width: 42px;
  height: 20px;
  margin: 12px 0 0 12px;
}

.text-group_3 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_4 {
  width: 10px;
  height: 6px;
  margin-top: 8px;
}

.group_9 {
  box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 1);
  width: 406px;
  height: 46px;
  margin-left: 477px;
}

.text_14 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(173, 173, 173, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 20px;
  margin: 12px 0 0 12px;
}

.thumbnail_5 {
  width: 14px;
  height: 14px;
  margin: 16px 16px 0 336px;
}

.group_10 {
  width: 1049px;
  height: 22px;
  margin: 60px 0 0 369px;
}

.image-text_4 {
  width: 81px;
  height: 22px;
}

.text-group_4 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
}

.thumbnail_6 {
  width: 8px;
  height: 12px;
  margin-top: 7px;
}

.image-text_5 {
  width: 50px;
  height: 22px;
  margin-left: 80px;
}

.text-group_5 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
}

.thumbnail_7 {
  width: 8px;
  height: 12px;
  margin-top: 7px;
}

.image-text_6 {
  width: 81px;
  height: 22px;
  margin-left: 80px;
}

.text-group_6 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
}

.thumbnail_8 {
  width: 8px;
  height: 12px;
  margin-top: 7px;
}

.image-text_7 {
  width: 81px;
  height: 22px;
  margin-left: 260px;
}

.text-group_7 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
}

.thumbnail_9 {
  width: 8px;
  height: 12px;
  margin-top: 7px;
}

.image-text_8 {
  width: 66px;
  height: 22px;
  margin-left: 270px;
}

.text-group_8 {
  width: 48px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
}

.thumbnail_10 {
  width: 8px;
  height: 12px;
  margin-top: 7px;
}

.group_11 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 1202px;
  height: 98px;
  margin: 12px 0 0 359px;
}

.text_15 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 10px;
}

.text_16 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 128px;
}

.text_17 {
  width: 300px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 20px 0 0 99px;
}

.text_18 {
  width: 300px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 20px 0 0 41px;
}

.text_19 {
  width: 111px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 51px;
}

.thumbnail_11 {
  width: 14px;
  height: 14px;
  margin: 27px 43px 0 41px;
}

.group_12 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 1202px;
  height: 98px;
  margin: 1px 0 0 359px;
}

.text_20 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 10px;
}

.text_21 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 128px;
}

.text_22 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 99px;
}

.text_23 {
  width: 300px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 20px 0 0 278px;
}

.text_24 {
  width: 111px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 51px;
}

.thumbnail_12 {
  width: 14px;
  height: 14px;
  margin: 27px 43px 0 41px;
}

.group_13 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 1202px;
  height: 98px;
  margin: 1px 0 0 359px;
}

.text_25 {
  width: 79px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 10px;
}

.text_26 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 81px;
}

.text_27 {
  width: 48px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 99px;
}

.text_28 {
  width: 300px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 20px 0 0 293px;
}

.text_29 {
  width: 111px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 51px;
}

.thumbnail_13 {
  width: 14px;
  height: 14px;
  margin: 27px 43px 0 41px;
}

.group_14 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 1202px;
  height: 98px;
  margin: 1px 0 0 359px;
}

.text_30 {
  width: 79px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 10px;
}

.text_31 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 81px;
}

.text_32 {
  width: 118px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 99px;
}

.text_33 {
  width: 300px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 20px 0 0 223px;
}

.text_34 {
  width: 111px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 51px;
}

.thumbnail_14 {
  width: 14px;
  height: 14px;
  margin: 27px 43px 0 41px;
}

.group_15 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 1202px;
  height: 98px;
  margin: 1px 0 0 359px;
}

.text_35 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 10px;
}

.text_36 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 128px;
}

.text_37 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 99px;
}

.text_38 {
  width: 300px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 20px 0 0 278px;
}

.text_39 {
  width: 111px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 51px;
}

.thumbnail_15 {
  width: 14px;
  height: 14px;
  margin: 27px 43px 0 41px;
}

.group_16 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 1202px;
  height: 98px;
  margin: 1px 0 0 359px;
}

.text_40 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 10px;
}

.text_41 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 128px;
}

.text_42 {
  width: 166px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 99px;
}

.text_43 {
  width: 300px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 20px 0 0 175px;
}

.text_44 {
  width: 111px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 51px;
}

.thumbnail_16 {
  width: 14px;
  height: 14px;
  margin: 27px 43px 0 41px;
}

.group_17 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 1202px;
  height: 98px;
  margin: 1px 0 0 359px;
}

.text_45 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 10px;
}

.text_46 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 128px;
}

.text_47 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 99px;
}

.text_48 {
  width: 300px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 20px 0 0 278px;
}

.text_49 {
  width: 111px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 22px;
  margin: 20px 0 0 51px;
}

.thumbnail_17 {
  width: 14px;
  height: 14px;
  margin: 27px 43px 0 41px;
}

.text-wrapper_2 {
  background-color: rgba(0, 0, 0, 1);
  height: 46px;
  width: 100px;
  margin: 40px 0 0 910px;
}

.text_50 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.group_18 {
  height: 230px;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  position: relative;
  margin: 100px 0 1px 443px;
}

.text-wrapper_3 {
  width: 409px;
  height: 22px;
  margin: 18px 0 0 60px;
}

.text_51 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_52 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_53 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text-wrapper_4 {
  width: 443px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_54 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_55 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_56 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 112px;
}

.text-wrapper_5 {
  width: 63px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_57 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text-wrapper_6 {
  width: 32px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_58 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_19 {
  width: 178px;
  height: 55px;
  margin: 30px 0 3px 20px;
}

.image-text_9 {
  width: 178px;
  height: 55px;
}

.image_1 {
  width: 56px;
  height: 55px;
}

.text-group_9 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin-top: 3px;
}

.group_20 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 236px;
  top: 170px;
  width: 1241px;
  height: 60px;
}
