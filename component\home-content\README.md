# 主页内容组件 (Home Content Component)

## 概述
主页内容组件包含了首页的核心内容区域，包括公司介绍、解决方案和新闻展示三个主要部分。

## 组件结构
```
component/home-content/
├── home-content.html    # 组件HTML模板
├── home-content.css     # 组件样式文件
├── home-content.js      # 组件JavaScript逻辑
└── README.md           # 组件说明文档
```

## 功能特性

### 1. 公司介绍区域
- 展示公司标题和描述信息
- 居中对齐的布局设计
- 响应式文字大小

### 2. 解决方案区域
- 黑色背景的醒目设计
- 包含标题和"更多详情"按钮
- 按钮悬停效果

### 3. 新闻区域
- 网格布局展示新闻卡片
- 响应式设计，支持不同屏幕尺寸
- 新闻卡片悬停动画效果
- 包含新闻标题、图片和摘要

## 使用方法

### 1. 引入组件文件
在HTML页面中引入组件的CSS和JavaScript文件：

```html
<!-- CSS文件 -->
<link rel="stylesheet" type="text/css" href="../component/home-content/home-content.css" />

<!-- JavaScript文件 -->
<script src="../component/home-content/home-content.js"></script>
```

### 2. 添加容器元素
在需要显示组件的位置添加容器元素：

```html
<div id="home-content-container"></div>
```

### 3. 自动加载
组件会在页面加载完成后自动加载并渲染到指定容器中。

## 交互功能

### 新闻卡片
- 悬停时会有向上移动的动画效果
- 点击卡片可触发跳转事件（需要自定义实现）

### 解决方案按钮
- 点击"更多详情"按钮可触发跳转事件（需要自定义实现）

### 浏览全部链接
- 点击"浏览全部"链接可跳转到新闻列表页面（需要自定义实现）

## 响应式设计

组件支持以下断点的响应式设计：

- **桌面端** (>1200px): 4列新闻网格
- **平板端** (900px-1200px): 3列新闻网格
- **小平板** (600px-900px): 2列新闻网格
- **手机端** (<600px): 1列新闻网格

## 自定义配置

### 修改新闻数据
新闻数据目前是硬编码在HTML中的，如需动态加载，可以：

1. 修改 `home-content.js` 文件
2. 添加数据获取逻辑
3. 动态生成新闻卡片HTML

### 样式自定义
可以通过修改 `home-content.css` 文件来自定义组件样式：

- 调整颜色主题
- 修改字体大小
- 调整间距和布局
- 自定义动画效果

## 依赖项
- jQuery 3.7.1+ (用于DOM操作)
- 现代浏览器支持 (支持CSS Grid和Flexbox)

## 注意事项
1. 图片路径相对于组件所在位置，确保图片文件存在
2. 字体文件需要正确引入（AlibabaPuHuiTi-Regular, PingFangSC-Medium等）
3. 组件依赖于容器元素的ID，确保页面中存在对应的容器元素
