.page {
  position: relative;
  width: 100vw;
  height: 102.3vw;
  overflow: hidden;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 100vw;
  height: 102.3vw;
}

.group_1 {
  background-image: url(./img/15e8b85ed4654c07b1c1d7c677702da8_mergeImage.png);
  width: 100vw;
  height: 27.09vw;
}

.block_1 {
  width: 75.68vw;
  height: 4.8vw;
}

.group_2 {
  border-radius: 50%;
  background-image: url(./img/29d58bed8ff843b5a2cd3433009363bf_mergeImage.png);
  width: 2.19vw;
  height: 2.19vw;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 1.2vw;
}

.text_1 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin: 1.09vw 0 0 0.62vw;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  width: 64.59vw;
  height: 4.8vw;
  margin-left: 2.14vw;
}

.text_2 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 13.85vw;
}

.text_3 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.03vw;
}

.text_4 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_5 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_6 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.thumbnail_1 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 2.03vw 23.22vw 0 2.08vw;
}

.block_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 76.98vw;
  height: 4.8vw;
  margin-left: 23.03vw;
}

.text_7 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.6vw;
}

.text_8 {
  width: 2.5vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 3.75vw;
}

.text_9 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 3.64vw;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 4.8vw;
  width: 6.36vw;
  margin: 0 23.28vw 0 29.89vw;
}

.text_10 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.71vw 0 0 1.56vw;
}

.text_11 {
  width: 10vw;
  height: 2.5vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 2.5vw;
  font-family: SourceHanSansCN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 3.75vw;
  margin: 6.61vw 0 0 60.2vw;
}

.text_12 {
  width: 16.05vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.25vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
  margin: 0.36vw 0 6.3vw 54.16vw;
}

.group_4 {
  width: 100vw;
  height: 75.27vw;
  margin-bottom: 0.06vw;
}

.text_13 {
  width: 45.37vw;
  height: 3.44vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.25vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.72vw;
  margin: 2.5vw 0 0 27.44vw;
}

.group_5 {
  width: 60.47vw;
  height: 2.4vw;
  margin: 4.27vw 0 0 18.69vw;
}

.group_6 {
  background-color: rgba(255, 255, 255, 1);
  width: 4.9vw;
  height: 2.4vw;
  border: 1px solid rgba(0, 0, 0, 1);
}

.image-text_1 {
  width: 3.65vw;
  height: 1.05vw;
  margin: 0.62vw 0 0 0.62vw;
}

.text-group_1 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_2 {
  width: 0.53vw;
  height: 0.32vw;
  margin-top: 0.42vw;
}

.group_7 {
  background-color: rgba(255, 255, 255, 1);
  width: 3.44vw;
  height: 2.4vw;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 1.36vw;
}

.image-text_2 {
  width: 2.19vw;
  height: 1.05vw;
  margin: 0.62vw 0 0 0.62vw;
}

.text-group_2 {
  width: 1.46vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_3 {
  width: 0.53vw;
  height: 0.32vw;
  margin-top: 0.42vw;
}

.group_8 {
  background-color: rgba(255, 255, 255, 1);
  width: 3.44vw;
  height: 2.4vw;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 1.36vw;
}

.image-text_3 {
  width: 2.19vw;
  height: 1.05vw;
  margin: 0.62vw 0 0 0.62vw;
}

.text-group_3 {
  width: 1.46vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_4 {
  width: 0.53vw;
  height: 0.32vw;
  margin-top: 0.42vw;
}

.group_9 {
  box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 1);
  width: 21.15vw;
  height: 2.4vw;
  margin-left: 24.85vw;
}

.text_14 {
  width: 1.46vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(173, 173, 173, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.62vw 0 0 0.62vw;
}

.thumbnail_5 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 0.83vw 0.83vw 0 17.5vw;
}

.group_10 {
  width: 54.64vw;
  height: 1.15vw;
  margin: 3.12vw 0 0 19.21vw;
}

.image-text_4 {
  width: 4.22vw;
  height: 1.15vw;
}

.text-group_4 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
}

.thumbnail_6 {
  width: 0.42vw;
  height: 0.63vw;
  margin-top: 0.37vw;
}

.image-text_5 {
  width: 2.61vw;
  height: 1.15vw;
  margin-left: 4.17vw;
}

.text-group_5 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
}

.thumbnail_7 {
  width: 0.42vw;
  height: 0.63vw;
  margin-top: 0.37vw;
}

.image-text_6 {
  width: 4.22vw;
  height: 1.15vw;
  margin-left: 4.17vw;
}

.text-group_6 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
}

.thumbnail_8 {
  width: 0.42vw;
  height: 0.63vw;
  margin-top: 0.37vw;
}

.image-text_7 {
  width: 4.22vw;
  height: 1.15vw;
  margin-left: 13.55vw;
}

.text-group_7 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
}

.thumbnail_9 {
  width: 0.42vw;
  height: 0.63vw;
  margin-top: 0.37vw;
}

.image-text_8 {
  width: 3.44vw;
  height: 1.15vw;
  margin-left: 14.07vw;
}

.text-group_8 {
  width: 2.5vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
}

.thumbnail_10 {
  width: 0.42vw;
  height: 0.63vw;
  margin-top: 0.37vw;
}

.group_11 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 62.61vw;
  height: 5.11vw;
  margin: 0.62vw 0 0 18.69vw;
}

.text_15 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 0.52vw;
}

.text_16 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 6.66vw;
}

.text_17 {
  width: 15.63vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 5.15vw;
}

.text_18 {
  width: 15.63vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 2.13vw;
}

.text_19 {
  width: 5.79vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 2.65vw;
}

.thumbnail_11 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 1.4vw 2.23vw 0 2.13vw;
}

.group_12 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 62.61vw;
  height: 5.11vw;
  margin: 0.05vw 0 0 18.69vw;
}

.text_20 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 0.52vw;
}

.text_21 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 6.66vw;
}

.text_22 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 5.15vw;
}

.text_23 {
  width: 15.63vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 14.47vw;
}

.text_24 {
  width: 5.79vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 2.65vw;
}

.thumbnail_12 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 1.4vw 2.23vw 0 2.13vw;
}

.group_13 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 62.61vw;
  height: 5.11vw;
  margin: 0.05vw 0 0 18.69vw;
}

.text_25 {
  width: 4.12vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 0.52vw;
}

.text_26 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 4.21vw;
}

.text_27 {
  width: 2.5vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 5.15vw;
}

.text_28 {
  width: 15.63vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 15.26vw;
}

.text_29 {
  width: 5.79vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 2.65vw;
}

.thumbnail_13 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 1.4vw 2.23vw 0 2.13vw;
}

.group_14 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 62.61vw;
  height: 5.11vw;
  margin: 0.05vw 0 0 18.69vw;
}

.text_30 {
  width: 4.12vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 0.52vw;
}

.text_31 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 4.21vw;
}

.text_32 {
  width: 6.15vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 5.15vw;
}

.text_33 {
  width: 15.63vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 11.61vw;
}

.text_34 {
  width: 5.79vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 2.65vw;
}

.thumbnail_14 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 1.4vw 2.23vw 0 2.13vw;
}

.group_15 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 62.61vw;
  height: 5.11vw;
  margin: 0.05vw 0 0 18.69vw;
}

.text_35 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 0.52vw;
}

.text_36 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 6.66vw;
}

.text_37 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 5.15vw;
}

.text_38 {
  width: 15.63vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 14.47vw;
}

.text_39 {
  width: 5.79vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 2.65vw;
}

.thumbnail_15 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 1.4vw 2.23vw 0 2.13vw;
}

.group_16 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 62.61vw;
  height: 5.11vw;
  margin: 0.05vw 0 0 18.69vw;
}

.text_40 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 0.52vw;
}

.text_41 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 6.66vw;
}

.text_42 {
  width: 8.65vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 5.15vw;
}

.text_43 {
  width: 15.63vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 9.11vw;
}

.text_44 {
  width: 5.79vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 2.65vw;
}

.thumbnail_16 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 1.4vw 2.23vw 0 2.13vw;
}

.group_17 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 62.61vw;
  height: 5.11vw;
  margin: 0.05vw 0 0 18.69vw;
}

.text_45 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 0.52vw;
}

.text_46 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 6.66vw;
}

.text_47 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 5.15vw;
}

.text_48 {
  width: 15.63vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 14.47vw;
}

.text_49 {
  width: 5.79vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.04vw 0 0 2.65vw;
}

.thumbnail_17 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 1.4vw 2.23vw 0 2.13vw;
}

.text-wrapper_2 {
  background-color: rgba(0, 0, 0, 1);
  height: 2.4vw;
  width: 5.21vw;
  margin: 2.08vw 0 0 47.39vw;
}

.text_50 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.group_18 {
  height: 11.98vw;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
  position: relative;
  margin: 5.2vw 0 0.05vw 23.07vw;
}

.text-wrapper_3 {
  width: 21.31vw;
  height: 1.15vw;
  margin: 0.93vw 0 0 3.12vw;
}

.text_51 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_52 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_53 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text-wrapper_4 {
  width: 23.08vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_54 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_55 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_56 {
  width: 4.95vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.84vw;
}

.text-wrapper_5 {
  width: 3.29vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_57 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text-wrapper_6 {
  width: 1.67vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_58 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.group_19 {
  width: 9.28vw;
  height: 2.87vw;
  margin: 1.56vw 0 0.15vw 1.04vw;
}

.image-text_9 {
  width: 9.28vw;
  height: 2.87vw;
}

.image_1 {
  width: 2.92vw;
  height: 2.87vw;
}

.text-group_9 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin-top: 0.16vw;
}

.group_20 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 12.3vw;
  top: 8.86vw;
  width: 64.64vw;
  height: 3.13vw;
}
