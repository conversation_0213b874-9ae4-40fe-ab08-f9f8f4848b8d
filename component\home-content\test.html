<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主页内容组件测试</title>
    <link rel="stylesheet" type="text/css" href="../../assets/common.css" />
    <link rel="stylesheet" type="text/css" href="./home-content.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .test-header h1 {
            color: #333;
            margin: 0;
        }
        .test-header p {
            color: #666;
            margin: 10px 0 0 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>主页内容组件测试页面</h1>
            <p>测试公司介绍、解决方案和新闻区域组件的显示效果</p>
        </div>
        
        <!-- 主页内容组件容器 -->
        <div id="home-content-container"></div>
    </div>

    <script src="../../assets/jquery-3.7.1.min.js"></script>
    <script src="./home-content.js"></script>
</body>
</html>
