html {
  font-size: 37.5px;
}

.page {
  position: relative;
  width: 51.2rem;
  height: 82.854rem;
  overflow: hidden;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 51.2rem;
  height: 82.854rem;
}

.box_17 {
  width: 38.747rem;
  height: 2.454rem;
}

.group_1 {
  border-radius: 50%;
  background-image: url(./img/e6efb85391a946f2a138254723f94e8a_mergeImage.png);
  width: 1.12rem;
  height: 1.12rem;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 0.614rem;
}

.text_1 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin: 0.56rem 0 0 0.32rem;
}

.group_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 33.067rem;
  height: 2.454rem;
  margin-left: 1.094rem;
}

.text_2 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 7.094rem;
}

.text_3 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.04rem;
}

.text_4 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_5 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_6 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.thumbnail_1 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 1.04rem 11.894rem 0 1.067rem;
}

.box_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 38.934rem;
  height: 2.454rem;
  margin-left: 12.267rem;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(14, 107, 228, 1);
  height: 2.454rem;
  width: 3.6rem;
}

.text_7 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.text_8 {
  width: 1.44rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.text_9 {
  width: 0.96rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.707rem;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 2.454rem;
  width: 3.254rem;
  margin: 0 11.92rem 0 15.2rem;
}

.text_10 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.827rem 0 0 0.694rem;
}

.box_18 {
  width: 6.454rem;
  height: 0.587rem;
  margin: 0.347rem 0 0 12.267rem;
}

.text_11 {
  width: 2.24rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.373rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.thumbnail_2 {
  width: 0.48rem;
  height: 0.48rem;
  margin: 0.027rem 0 0 0.16rem;
}

.text_12 {
  width: 1.12rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.373rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 0.16rem;
}

.thumbnail_3 {
  width: 0.48rem;
  height: 0.48rem;
  margin: 0.027rem 0 0 0.16rem;
}

.text_13 {
  width: 1.494rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 85, 195, 1);
  font-size: 0.373rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 0.16rem;
}

.box_4 {
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.12);
  height: 10.187rem;
  width: 38.4rem;
  margin: 0.294rem 0 0 6.4rem;
}

.text-wrapper_3 {
  background-image: url(./img/b8b7a955f3a14cf9a0e61eefe0bc06a3_mergeImage.png);
  width: 38.4rem;
  height: 10.187rem;
}

.text_14 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 9.2rem 0 0 16.854rem;
}

.text_15 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 9.2rem 0 0 1.067rem;
}

.text_16 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 9.2rem 16.854rem 0 1.067rem;
}

.box_5 {
  background-color: rgba(0, 85, 195, 1);
  width: 1.92rem;
  height: 0.08rem;
  margin-left: 22.72rem;
}

.text_17 {
  width: 23.227rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: right;
  line-height: 0.72rem;
  margin: 1.014rem 0 0 13.04rem;
}

.text_18 {
  width: 3.174rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 0.72rem;
  margin-left: 33.094rem;
}

.box_6 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 27.467rem;
  height: 22.347rem;
  margin: 1.12rem 0 0 11.867rem;
}

.text_19 {
  width: 1.894rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.334rem;
  margin: 0.454rem 0 0 1.707rem;
}

.text_20 {
  width: 8.4rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.64rem;
  margin: 0.374rem 0 0 1.707rem;
}

.text-wrapper_4 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
  margin: 0.187rem 0 0 1.707rem;
}

.text_21 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
}

.paragraph_1 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.64rem;
}

.text-wrapper_5 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
  margin: 0.107rem 0 0 1.707rem;
}

.text_22 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
}

.paragraph_2 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.64rem;
}

.text-wrapper_6 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
  margin: 0.107rem 0 0 1.707rem;
}

.text_23 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
}

.paragraph_3 {
  width: 24.08rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.64rem;
}

.text_24 {
  width: 8.4rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.64rem;
  margin: 0.294rem 0 0 1.707rem;
}

.text-wrapper_7 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
  margin: 0.187rem 0 0 1.707rem;
}

.text_25 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
}

.paragraph_4 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.64rem;
}

.text-wrapper_8 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
  margin: 0.107rem 0 0 1.707rem;
}

.text_26 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
}

.paragraph_5 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.64rem;
}

.text-wrapper_9 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
  margin: 0.107rem 0 0.534rem 1.707rem;
}

.text_27 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 0.64rem;
}

.paragraph_6 {
  width: 24.08rem;
  height: 2.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.64rem;
}

.image-text_12 {
  width: 2.96rem;
  height: 1.334rem;
  margin: 1.52rem 0 0 11.867rem;
}

.label_1 {
  width: 0.8rem;
  height: 0.8rem;
  margin-top: 0.347rem;
}

.text-group_1 {
  width: 1.894rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.334rem;
}

.box_7 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 8.8rem;
  width: 27.467rem;
  margin: 0.614rem 0 0 11.867rem;
}

.group_3 {
  border-radius: 8px;
  background-image: url(./img/da36ba129190407d9f85de78f5f3456d_mergeImage.png);
  width: 27.467rem;
  height: 8.8rem;
}

.image-text_13 {
  width: 5.36rem;
  height: 0.827rem;
  margin: 0.88rem 0 0 16.08rem;
}

.group_4 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.214rem;
  height: 0.214rem;
  margin-top: 0.294rem;
}

.text-group_2 {
  width: 4.88rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.image-text_14 {
  width: 4.96rem;
  height: 0.827rem;
  margin: 0.427rem 0 0 16.08rem;
}

.block_2 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.214rem;
  height: 0.214rem;
  margin-top: 0.294rem;
}

.text-group_3 {
  width: 4.48rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.image-text_15 {
  width: 6.214rem;
  height: 0.827rem;
  margin: 0.427rem 0 0 16.08rem;
}

.box_8 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.214rem;
  height: 0.214rem;
  margin-top: 0.294rem;
}

.text-group_4 {
  width: 5.734rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.image-text_16 {
  width: 4.534rem;
  height: 0.827rem;
  margin: 0.427rem 0 0 16.08rem;
}

.group_5 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.214rem;
  height: 0.214rem;
  margin-top: 0.294rem;
}

.text-group_5 {
  width: 4.054rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.image-text_17 {
  width: 6.214rem;
  height: 0.827rem;
  margin: 0.427rem 0 0 16.08rem;
}

.box_9 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.214rem;
  height: 0.214rem;
  margin-top: 0.294rem;
}

.text-group_6 {
  width: 5.734rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.image-text_18 {
  width: 5.787rem;
  height: 0.827rem;
  margin: 0.427rem 0 0.827rem 16.08rem;
}

.section_1 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.214rem;
  height: 0.214rem;
  margin-top: 0.294rem;
}

.text-group_7 {
  width: 5.307rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.box_10 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 27.467rem;
  height: 4.507rem;
  margin: 0.534rem 0 0 11.867rem;
}

.text-group_13 {
  width: 24.8rem;
  height: 3.067rem;
  margin: 0.694rem 0 0 1.334rem;
}

.text_28 {
  width: 4.747rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.72rem;
}

.text_29 {
  width: 24.8rem;
  height: 1.92rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.64rem;
  margin-top: 0.427rem;
}

.image-text_19 {
  width: 2.96rem;
  height: 1.334rem;
  margin: 1.52rem 0 0 11.84rem;
}

.label_2 {
  width: 0.8rem;
  height: 0.8rem;
  margin-top: 0.347rem;
}

.text-group_9 {
  width: 1.894rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.334rem;
}

.box_11 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 5.174rem;
  width: 27.467rem;
  margin: 0.614rem 0 0 11.867rem;
}

.box_12 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  position: relative;
  width: 27.467rem;
  height: 5.174rem;
}

.image-text_20 {
  width: 3.44rem;
  height: 1.974rem;
  margin: 1.627rem 0 0 1.6rem;
}

.group_6 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.214rem;
  height: 0.214rem;
  margin-top: 1.44rem;
}

.text-group_14 {
  width: 2.96rem;
  height: 1.974rem;
}

.text_30 {
  width: 1.68rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.text_31 {
  width: 2.96rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
  margin-top: 0.32rem;
}

.image-text_21 {
  position: absolute;
  left: 1.6rem;
  top: 1.627rem;
  width: 3.44rem;
  height: 1.974rem;
}

.box_13 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.214rem;
  height: 0.214rem;
  margin-top: 0.294rem;
}

.text-group_15 {
  width: 2.96rem;
  height: 1.974rem;
}

.text_30 {
  width: 1.68rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.text_31 {
  width: 2.96rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
  margin-top: 0.32rem;
}

.box_14 {
  height: 4.534rem;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
  margin: 1.6rem 0 0 11.867rem;
}

.text-wrapper_16 {
  width: 2.534rem;
  height: 0.88rem;
  margin: 0.694rem 0 0 1.334rem;
}

.text_32 {
  width: 2.534rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.64rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.box_19 {
  width: 22.907rem;
  height: 1.28rem;
  margin: 0.08rem 0 1.6rem 1.334rem;
}

.text_33 {
  width: 17.04rem;
  height: 1.067rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.214rem;
}

.text-wrapper_11 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
}

.text_34 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.box_16 {
  height: 6.134rem;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
  position: relative;
  margin: 1.6rem 0 0 11.867rem;
}

.text-wrapper_17 {
  width: 10.907rem;
  height: 0.587rem;
  margin: 0.48rem 0 0 1.6rem;
}

.text_35 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_36 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_37 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text-wrapper_18 {
  width: 11.814rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_38 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_39 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_40 {
  width: 2.534rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.987rem;
}

.text-wrapper_19 {
  width: 1.68rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_41 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text-wrapper_20 {
  width: 0.854rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_42 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.block_5 {
  width: 4.747rem;
  height: 1.467rem;
  margin: 0.8rem 0 0.08rem 0.534rem;
}

.image-text_22 {
  width: 4.747rem;
  height: 1.467rem;
}

.image_1 {
  width: 1.494rem;
  height: 1.467rem;
}

.text-group_12 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin-top: 0.08rem;
}

.block_4 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 6.24rem;
  top: 4.534rem;
  width: 33.094rem;
  height: 1.6rem;
}
