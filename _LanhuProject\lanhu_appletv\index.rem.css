html {
  font-size: 37.5px;
}

.page {
  position: relative;
  width: 51.2rem;
  height: 69.707rem;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 51.2rem;
  height: 69.707rem;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 51.2rem;
  height: 2.454rem;
}

.text_1 {
  width: 1.84rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 1.44rem 0 0 7.627rem;
}

.image_1 {
  width: 1.494rem;
  height: 1.467rem;
  margin: 0.48rem 0 0 2.8rem;
}

.text_2 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin: 0.56rem 0 0 0.107rem;
}

.text_3 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 8.16rem;
}

.text_4 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.text_5 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.text_6 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.text_7 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.thumbnail_1 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 1.04rem 11.92rem 0 0.854rem;
}

.block_2 {
  position: relative;
  width: 51.2rem;
  height: 67.28rem;
  margin-bottom: 0.027rem;
}

.block_3 {
  width: 44.32rem;
  height: 2.454rem;
  margin-left: 6.88rem;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(14, 107, 228, 1);
  height: 2.454rem;
  width: 3.147rem;
}

.text_8 {
  width: 1.44rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.group_2 {
  background-color: rgba(9, 9, 9, 1);
  position: relative;
  width: 38.934rem;
  height: 2.454rem;
}

.text_9 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.text_10 {
  width: 1.44rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.334rem;
}

.text_11 {
  width: 0.96rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.334rem;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 2.454rem;
  width: 3.254rem;
  margin: 0 11.92rem 0 15.947rem;
}

.text_12 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.827rem 0 0 0.694rem;
}

.image_2 {
  position: absolute;
  left: -2.24rem;
  top: 1.2rem;
  width: 2.507rem;
  height: 0.027rem;
}

.block_4 {
  width: 38.4rem;
  height: 10.187rem;
  margin-left: 6.4rem;
}

.box_1 {
  background-image: url(./img/ddcfe9406c794a5ba905a5b3700a9200_mergeImage.png);
  width: 38.4rem;
  height: 10.187rem;
  border: 1px solid rgba(151, 151, 151, 1);
}

.text_13 {
  width: 22.347rem;
  height: 1.734rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.28rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.734rem;
  margin: 2.454rem 0 0 7.787rem;
}

.text-wrapper_3 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
  margin: 1.467rem 0 0 17.867rem;
}

.text_14 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.image_3 {
  width: 3.414rem;
  height: 0.374rem;
  margin: 2.427rem 0 0.507rem 17.494rem;
}

.text-wrapper_4 {
  width: 3.787rem;
  height: 1.334rem;
  margin: 1.52rem 0 0 13.6rem;
}

.text_15 {
  width: 3.787rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.334rem;
}

.block_5 {
  width: 28.614rem;
  height: 6.694rem;
  margin: 0.614rem 0 0 6.507rem;
}

.group_3 {
  width: 2.667rem;
  height: 2.24rem;
  margin-top: 2.774rem;
}

.text_16 {
  width: 1.84rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin-left: 0.374rem;
}

.text-wrapper_5 {
  background-color: rgba(14, 107, 228, 1);
  height: 1.227rem;
  margin-top: 0.48rem;
  width: 2.667rem;
}

.text_17 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.48rem;
}

.group_4 {
  background-color: rgba(244, 244, 244, 1);
  position: relative;
  width: 7.494rem;
  height: 6.694rem;
  margin-left: 2.374rem;
}

.text-group_1 {
  width: 2.534rem;
  height: 1.627rem;
  margin: 0.747rem 0 0 0.534rem;
}

.text_18 {
  width: 2.214rem;
  height: 0.774rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.56rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.774rem;
}

.text_19 {
  width: 2.534rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-top: 0.267rem;
}

.text-wrapper_6 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
  margin: 1.387rem 0 1.707rem 0.534rem;
}

.text_20 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.image_4 {
  position: absolute;
  left: -2.08rem;
  top: 4.374rem;
  width: 2.507rem;
  height: 0.027rem;
}

.image-text_1 {
  width: 2.64rem;
  height: 0.614rem;
  margin: 5.68rem 0 0 0.267rem;
}

.image_5 {
  width: 0.8rem;
  height: 0.507rem;
  margin-top: 0.107rem;
}

.text-group_2 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.group_5 {
  background-color: rgba(216, 216, 216, 0.3);
  width: 7.494rem;
  height: 6.694rem;
  margin-left: 3.44rem;
}

.text-group_3 {
  width: 5.494rem;
  height: 2.72rem;
  margin: 0.747rem 0 0 0.534rem;
}

.text_21 {
  width: 1.654rem;
  height: 0.774rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.56rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.774rem;
}

.text_22 {
  width: 5.494rem;
  height: 1.6rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.347rem;
}

.text-wrapper_7 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
  margin: 0.294rem 0 1.707rem 0.534rem;
}

.text_23 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.image-text_2 {
  width: 1.974rem;
  height: 0.614rem;
  margin: 5.787rem 0 0 0.267rem;
}

.thumbnail_2 {
  width: 0.534rem;
  height: 0.534rem;
  margin-top: 0.08rem;
}

.text-group_4 {
  width: 1.28rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.block_6 {
  width: 32.427rem;
  height: 6.694rem;
  margin: 1.067rem 0 0 3.094rem;
}

.box_2 {
  width: 2.08rem;
  height: 2.427rem;
  margin-top: 1.387rem;
}

.text-wrapper_8 {
  background-color: rgba(216, 216, 216, 1);
  height: 0.907rem;
  border: 1px solid rgba(151, 151, 151, 1);
  width: 2.08rem;
}

.text_24 {
  width: 1.12rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.16rem 0 0 0.48rem;
}

.text-wrapper_9 {
  background-color: rgba(216, 216, 216, 1);
  height: 0.907rem;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 0.614rem;
  width: 2.08rem;
}

.text_25 {
  width: 1.12rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.16rem 0 0 0.48rem;
}

.box_3 {
  background-color: rgba(244, 244, 244, 1);
  width: 7.494rem;
  height: 6.694rem;
  margin-left: 6.374rem;
}

.text-group_5 {
  width: 5.814rem;
  height: 2.694rem;
  margin: 0.747rem 0 0 0.534rem;
}

.text_26 {
  width: 1.12rem;
  height: 0.774rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.56rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.774rem;
}

.text_27 {
  width: 5.814rem;
  height: 1.6rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.32rem;
}

.text-wrapper_10 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
  margin: 0.72rem 0 1.307rem 0.534rem;
}

.text_28 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.image-text_3 {
  width: 2.32rem;
  height: 0.614rem;
  margin: 5.68rem 0 0 0.587rem;
}

.thumbnail_3 {
  width: 0.48rem;
  height: 0.534rem;
  margin-top: 0.08rem;
}

.text-group_6 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.box_4 {
  background-color: rgba(216, 216, 216, 0.3);
  width: 7.494rem;
  height: 6.694rem;
  margin-left: 3.44rem;
}

.text-group_7 {
  width: 5.547rem;
  height: 3.254rem;
  margin: 0.747rem 0 0 0.534rem;
}

.text_29 {
  width: 2.214rem;
  height: 0.774rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.56rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.774rem;
}

.text_30 {
  width: 5.547rem;
  height: 2.134rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.347rem;
}

.text-wrapper_11 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
  margin: 0.16rem 0 1.307rem 0.534rem;
}

.text_31 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.image-text_4 {
  width: 2.374rem;
  height: 0.614rem;
  margin: 5.787rem 0 0 0.267rem;
}

.thumbnail_4 {
  width: 0.534rem;
  height: 0.534rem;
  margin-top: 0.08rem;
}

.text-group_8 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.block_7 {
  width: 23.227rem;
  height: 3.174rem;
  margin: 1.787rem 0 0 13.68rem;
}

.text-group_9 {
  width: 23.227rem;
  height: 3.174rem;
}

.text_32 {
  width: 9.467rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.334rem;
  margin-left: 7.2rem;
}

.text_33 {
  width: 23.227rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 0.72rem;
  margin-top: 0.4rem;
}

.block_8 {
  width: 27.467rem;
  height: 3.2rem;
  margin: 1.494rem 0 0 11.547rem;
}

.section_1 {
  background-color: rgba(0, 0, 0, 1);
  width: 27.467rem;
  height: 3.2rem;
}

.text_34 {
  width: 2.56rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 1.147rem 0 0 5.494rem;
}

.text-wrapper_12 {
  background-image: url(./img/583d9fa2bf8848e38fcfc6894cd7d9c1_mergeImage.png);
  height: 3.2rem;
  margin-left: 5.787rem;
  width: 13.627rem;
}

.text_35 {
  width: 2.56rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 1.147rem 0 0 5.547rem;
}

.block_9 {
  width: 30.4rem;
  height: 1.467rem;
  margin: 1.387rem 0 0 13.6rem;
}

.text_36 {
  width: 3.787rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.334rem;
  margin-top: 0.134rem;
}

.image-text_5 {
  width: 2.134rem;
  height: 0.534rem;
  margin: 0.72rem 0 0 19.334rem;
}

.text-group_10 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(118, 118, 118, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_5 {
  width: 0.374rem;
  height: 0.374rem;
  margin-top: 0.107rem;
}

.image_6 {
  width: 2.054rem;
  height: 0.027rem;
  margin: 0.987rem 0 0 0.534rem;
}

.box_5 {
  width: 2.134rem;
  height: 1.254rem;
  margin-left: 0.427rem;
}

.text_37 {
  width: 1.84rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.image-text_6 {
  width: 2.134rem;
  height: 0.534rem;
  margin-top: 0.187rem;
}

.text-group_11 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 58, 133, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_6 {
  width: 0.374rem;
  height: 0.374rem;
  margin-top: 0.107rem;
}

.block_10 {
  width: 27.467rem;
  height: 9.014rem;
  margin: 0.614rem 0 0 11.547rem;
}

.list_1 {
  width: 27.467rem;
  height: 9.014rem;
  justify-content: space-between;
}

.image-text_7-0 {
  background-color: rgba(244, 244, 244, 1);
  width: 6.667rem;
  height: 9.014rem;
  margin-right: 0.267rem;
}

.group_6-0 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 6.667rem;
  height: 3.414rem;
  background: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
}

.text-group_12-0 {
  width: 5.547rem;
  height: 2.8rem;
  margin: 0.267rem 0 2.534rem 0.534rem;
}

.text_38-0 {
  width: 5.547rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.72rem;
}

.text_39-0 {
  width: 5.547rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.587rem;
  margin-top: 0.187rem;
}

.image-text_7-1 {
  background-color: rgba(244, 244, 244, 1);
  width: 6.667rem;
  height: 9.014rem;
  margin-right: 0.267rem;
}

.group_6-1 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 6.667rem;
  height: 3.414rem;
  background: url(./img/48c86bd3e7d74ef4a7d049ec56123b91_mergeImage.png);
}

.text-group_12-1 {
  width: 5.547rem;
  height: 2.8rem;
  margin: 0.267rem 0 2.534rem 0.534rem;
}

.text_38-1 {
  width: 5.547rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.72rem;
}

.text_39-1 {
  width: 5.547rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.587rem;
  margin-top: 0.187rem;
}

.image-text_7-2 {
  background-color: rgba(244, 244, 244, 1);
  width: 6.667rem;
  height: 9.014rem;
  margin-right: 0.267rem;
}

.group_6-2 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 6.667rem;
  height: 3.414rem;
  background: url(./img/dba985fbe79b4349ae38dd05d22c4ce0_mergeImage.png);
}

.text-group_12-2 {
  width: 5.547rem;
  height: 2.8rem;
  margin: 0.267rem 0 2.534rem 0.534rem;
}

.text_38-2 {
  width: 5.547rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.72rem;
}

.text_39-2 {
  width: 5.547rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.587rem;
  margin-top: 0.187rem;
}

.image-text_7-3 {
  background-color: rgba(244, 244, 244, 1);
  width: 6.667rem;
  height: 9.014rem;
  margin-right: 0.267rem;
}

.group_6-3 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 6.667rem;
  height: 3.414rem;
  background: url(./img/1f5b9ade7e9240f0bcbb8fcaec428af7_mergeImage.png);
}

.text-group_12-3 {
  width: 5.547rem;
  height: 2.8rem;
  margin: 0.267rem 0 2.534rem 0.534rem;
}

.text_38-3 {
  width: 5.547rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.72rem;
}

.text_39-3 {
  width: 5.547rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.587rem;
  margin-top: 0.187rem;
}

.block_11 {
  width: 27.467rem;
  height: 4.534rem;
  margin: 1.6rem 0 0 11.547rem;
}

.box_6 {
  height: 4.534rem;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
}

.text-wrapper_13 {
  width: 2.534rem;
  height: 0.88rem;
  margin: 0.694rem 0 0 1.334rem;
}

.text_40 {
  width: 2.534rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.64rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.box_7 {
  width: 22.907rem;
  height: 1.28rem;
  margin: 0.08rem 0 1.6rem 1.334rem;
}

.text_41 {
  width: 17.04rem;
  height: 1.067rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.214rem;
}

.text-wrapper_14 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
}

.text_42 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.block_12 {
  width: 27.467rem;
  height: 6.134rem;
  margin: 2.294rem 0 0.027rem 11.547rem;
}

.section_2 {
  height: 6.134rem;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
  position: relative;
}

.text-wrapper_15 {
  width: 10.907rem;
  height: 0.587rem;
  margin: 0.48rem 0 0 1.6rem;
}

.text_43 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_44 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_45 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text-wrapper_16 {
  width: 11.814rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_46 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_47 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_48 {
  width: 2.534rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.987rem;
}

.text-wrapper_17 {
  width: 1.68rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_49 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text-wrapper_18 {
  width: 0.854rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_50 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.group_7 {
  width: 4.747rem;
  height: 1.467rem;
  margin: 0.8rem 0 0.08rem 0.534rem;
}

.image-text_8 {
  width: 4.747rem;
  height: 1.467rem;
}

.image_7 {
  width: 1.494rem;
  height: 1.467rem;
}

.text-group_13 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin-top: 0.08rem;
}

.group_8 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 6.56rem;
  top: 4.534rem;
  width: 33.094rem;
  height: 1.6rem;
}

.block_13 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 4.427rem;
  width: 7.2rem;
  position: absolute;
  left: 17.92rem;
  top: 16.48rem;
}

.box_8 {
  background-image: url(./img/2e3a71974cfc4afc8c36dc3a32bc9d95_mergeImage.png);
  width: 7.2rem;
  height: 4.427rem;
}

.block_14 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 4.427rem;
  width: 7.2rem;
  position: absolute;
  left: 31.84rem;
  top: 16.4rem;
}

.group_9 {
  background-image: url(./img/1b68b0c36f9e41caa0ec479a3ca737db_mergeImage.png);
  width: 7.2rem;
  height: 4.427rem;
}

.block_15 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 4.427rem;
  width: 7.2rem;
  position: absolute;
  left: 17.92rem;
  top: 24.24rem;
}

.box_9 {
  background-image: url(./img/8a237950cd5a4340a3dc10bcb2cf5975_mergeImage.png);
  width: 7.2rem;
  height: 4.427rem;
}

.block_16 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 4.427rem;
  width: 7.2rem;
  position: absolute;
  left: 31.84rem;
  top: 24.16rem;
}

.group_10 {
  background-image: url(./img/2a5b1018e10441e6aae6f6edb879113e_mergeImage.png);
  width: 7.2rem;
  height: 4.427rem;
}
