/* 页面基础样式 */
.page {
	width: 100%;
	min-height: 100vh;
	background-color: #ffffff;
}

.main-content {
	max-width: 1920px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.section-header {
	text-align: center;
	margin-bottom: 60px;
}

.section-title {
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #000000;
	line-height: 1.4;
	margin: 0;
	font-weight: 600;
}

/* 产品预览区域样式 */
.product-preview-section {
	padding: 80px 0;
	background-color: #ffffff;
}

.product-grid {
	display: flex;
	flex-direction: column;
	gap: 40px;
}

.product-row {
	display: flex;
	justify-content: space-between;
	gap: 30px;
	align-items: stretch;
}

.product-card {
	width: calc(50% - 15px);
	height: 280px;
	display: flex;
	position: relative;
	transition: all 0.3s ease;
	overflow: visible;
}

.product-left {
	background-color: #f4f4f4;
	border-radius: 12px;
	padding: 28px 20px;
	width: 60%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	position: relative;
	z-index: 1;
}

.product-right {
	width: 40%;
	display: flex;
	flex-direction: column;
	position: relative;
}

.product-card:hover {
	transform: translateY(-4px);
}

.product-card:hover .product-left {
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.product-image-container {
	position: relative;
	height: 60%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: -30px;
	z-index: 2;
}

.product-image {
	width: 120px;
	height: auto;
	object-fit: contain;
	max-height: 100%;
}

.product-icon-section {
	height: 40%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 8px;
	background-color: #ffffff;
	padding: 15px;
	border-radius: 8px;
	margin-top: 10px;
}

.product-icon {
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.icon-label {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #666666;
	text-align: center;
	line-height: 1.2;
}

.product-content {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;
}

.product-info {
	flex: 1;
	margin-bottom: 20px;
}

.product-category {
	font-size: 21px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #000000;
	line-height: 29px;
	margin: 0 0 10px 0;
	font-weight: normal;
	white-space: nowrap;
}

.product-description {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Light, sans-serif;
	color: #666666;
	line-height: 1.5;
	margin: 0;
	font-weight: 300;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.product-action {
	margin-top: auto;
}

.learn-more-btn {
	display: inline-flex;
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #ffffff;
	text-decoration: none;
	padding: 10px 20px;
	background-color: rgba(236, 41, 20, 1);
	border: none;
	border-radius: 4px;
	transition: all 0.3s ease;
	line-height: 1.2;
	white-space: nowrap;
	align-items: center;
	justify-content: center;
	min-width: 80px;
}

.learn-more-btn:hover {
	background-color: rgba(200, 35, 15, 1);
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(236, 41, 20, 0.3);
}

.product-image {
	position: absolute;
	bottom: 164px;
	right: -78px;
	width: 94px;
	height: 1px;
	object-fit: contain;
}

.product-thumbnail-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	padding: 10px;
}

.thumbnail-image {
	width: 20px;
	height: 20px;
	object-fit: contain;
	transition: transform 0.3s ease;
}

.thumbnail-image:hover {
	transform: scale(1.1);
}

.thumbnail-label {
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular, sans-serif;
	color: #000000;
	line-height: 1.4;
	text-align: center;
}

/* 响应式设计 - 必须包含的媒体查询 */
@media (max-width: 1200px) {
	.container {
		max-width: 960px;
		padding: 0 16px;
	}

	.product-row {
		gap: 20px;
	}

	.product-card {
		width: calc(50% - 10px);
		height: 240px;
	}

	.product-left {
		padding: 20px 16px;
	}

	.product-image {
		width: 100px;
	}

	.section-title {
		font-size: 32px;
	}

	.product-card {
		padding: 24px 16px;
		height: 220px;
	}
}

@media (max-width: 900px) {
	.product-preview-section {
		padding: 60px 0;
	}

	.section-header {
		margin-bottom: 40px;
	}

	.section-title {
		font-size: 28px;
	}

	.product-row {
		flex-direction: column;
		gap: 20px;
	}

	.product-card {
		width: 100%;
		height: auto;
		min-height: 200px;
		flex-direction: column;
	}

	.product-left {
		width: 100%;
		padding: 20px;
		margin-bottom: 15px;
	}

	.product-right {
		width: 100%;
		flex-direction: row;
		align-items: center;
		gap: 15px;
	}

	.product-image-container {
		margin-left: 0;
		height: auto;
		width: 60%;
	}

	.product-icon-section {
		width: 40%;
		height: auto;
		margin-top: 0;
		padding: 10px;
	}

	.product-image {
		position: static;
		width: 60px;
		margin-top: 16px;
		align-self: flex-end;
	}

	.product-thumbnail-card {
		flex-direction: row;
		justify-content: center;
		padding: 16px;
		background-color: rgba(248, 249, 250, 0.8);
		border-radius: 8px;
	}

	.thumbnail-image {
		width: 24px;
		height: 24px;
	}
}

@media (max-width: 600px) {
	.product-preview-section {
		padding: 40px 0;
		margin-top: 40px;
	}

	.container {
		padding: 0 12px;
	}

	.section-title {
		font-size: 24px;
	}

	.product-grid {
		gap: 24px;
	}

	.product-card {
		width: 100%;
		min-height: 180px;
	}

	.product-left {
		padding: 16px;
	}

	.product-category {
		font-size: 16px;
		margin-bottom: 8px;
	}

	.product-description {
		font-size: 13px;
		line-height: 1.4;
	}

	.learn-more-btn {
		font-size: 13px;
		padding: 6px 14px;
		width: 80px;
		height: 36px;
	}

	.product-action {
		margin-top: 30px;
	}

	.product-image {
		width: 50px;
		margin-top: 12px;
	}

	.thumbnail-label {
		font-size: 14px;
	}
}

/* 公司介绍、解决方案、新闻区域样式已移至 ../component/home-content/home-content.css */

/* 联系我们样式已移至 ../component/contact/contact.css */

/* 页脚样式已移至 ../component/footer/footer.css */
