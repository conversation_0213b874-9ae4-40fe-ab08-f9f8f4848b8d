<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>联系我们组件测试</title>
		<link rel="stylesheet" type="text/css" href="contact.css" />
	</head>
	<body>
		<h1>联系我们组件测试页面</h1>

		<!-- 联系我们组件容器 -->
		<div id="contact-container"></div>

		<script>
			// 简单的组件加载器（用于测试）
			function loadContact() {
				fetch("./contact.html")
					.then((response) => response.text())
					.then((html) => {
						const contactContainer =
							document.getElementById("contact-container");
						if (contactContainer) {
							contactContainer.innerHTML = html;
						}
					})
					.catch((error) => {
						console.error("联系我们组件加载失败:", error);
					});
			}

			// 页面加载完成后执行
			if (document.readyState === "loading") {
				document.addEventListener("DOMContentLoaded", loadContact);
			} else {
				loadContact();
			}
		</script>
	</body>
</html>
