.page {
  position: relative;
  width: 100vw;
  height: 161.83vw;
  overflow: hidden;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 100vw;
  height: 161.83vw;
}

.box_17 {
  width: 75.68vw;
  height: 4.8vw;
}

.group_1 {
  border-radius: 50%;
  background-image: url(./img/e6efb85391a946f2a138254723f94e8a_mergeImage.png);
  width: 2.19vw;
  height: 2.19vw;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 1.2vw;
}

.text_1 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin: 1.09vw 0 0 0.62vw;
}

.group_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 64.59vw;
  height: 4.8vw;
  margin-left: 2.14vw;
}

.text_2 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 13.85vw;
}

.text_3 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.03vw;
}

.text_4 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_5 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_6 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.thumbnail_1 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 2.03vw 23.22vw 0 2.08vw;
}

.box_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 76.05vw;
  height: 4.8vw;
  margin-left: 23.96vw;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(14, 107, 228, 1);
  height: 4.8vw;
  width: 7.04vw;
}

.text_7 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.text_8 {
  width: 2.82vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.text_9 {
  width: 1.88vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 3.33vw;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 4.8vw;
  width: 6.36vw;
  margin: 0 23.28vw 0 29.68vw;
}

.text_10 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.61vw 0 0 1.35vw;
}

.box_18 {
  width: 12.61vw;
  height: 1.15vw;
  margin: 0.67vw 0 0 23.95vw;
}

.text_11 {
  width: 4.38vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.72vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.thumbnail_2 {
  width: 0.94vw;
  height: 0.94vw;
  margin: 0.05vw 0 0 0.31vw;
}

.text_12 {
  width: 2.19vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.72vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 0.32vw;
}

.thumbnail_3 {
  width: 0.94vw;
  height: 0.94vw;
  margin: 0.05vw 0 0 0.31vw;
}

.text_13 {
  width: 2.92vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 85, 195, 1);
  font-size: 0.72vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 0.32vw;
}

.box_4 {
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.12);
  height: 19.9vw;
  width: 75vw;
  margin: 0.57vw 0 0 12.5vw;
}

.text-wrapper_3 {
  background-image: url(./img/b8b7a955f3a14cf9a0e61eefe0bc06a3_mergeImage.png);
  width: 75vw;
  height: 19.9vw;
}

.text_14 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 17.96vw 0 0 32.91vw;
}

.text_15 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 17.96vw 0 0 2.08vw;
}

.text_16 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 17.96vw 32.91vw 0 2.08vw;
}

.box_5 {
  background-color: rgba(0, 85, 195, 1);
  width: 3.75vw;
  height: 0.16vw;
  margin-left: 44.38vw;
}

.text_17 {
  width: 45.37vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: right;
  line-height: 1.41vw;
  margin: 1.97vw 0 0 25.46vw;
}

.text_18 {
  width: 6.2vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 1.41vw;
  margin-left: 64.64vw;
}

.box_6 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 53.65vw;
  height: 43.65vw;
  margin: 2.18vw 0 0 23.17vw;
}

.text_19 {
  width: 3.7vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.61vw;
  margin: 0.88vw 0 0 3.33vw;
}

.text_20 {
  width: 16.41vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.25vw;
  margin: 0.72vw 0 0 3.33vw;
}

.text-wrapper_4 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
  margin: 0.36vw 0 0 3.33vw;
}

.text_21 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
}

.paragraph_1 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.25vw;
}

.text-wrapper_5 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
  margin: 0.2vw 0 0 3.33vw;
}

.text_22 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
}

.paragraph_2 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.25vw;
}

.text-wrapper_6 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
  margin: 0.2vw 0 0 3.33vw;
}

.text_23 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
}

.paragraph_3 {
  width: 47.04vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.25vw;
}

.text_24 {
  width: 16.41vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.25vw;
  margin: 0.57vw 0 0 3.33vw;
}

.text-wrapper_7 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
  margin: 0.36vw 0 0 3.33vw;
}

.text_25 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
}

.paragraph_4 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.25vw;
}

.text-wrapper_8 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
  margin: 0.2vw 0 0 3.33vw;
}

.text_26 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
}

.paragraph_5 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.25vw;
}

.text-wrapper_9 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
  margin: 0.2vw 0 1.04vw 3.33vw;
}

.text_27 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.9);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 1.25vw;
}

.paragraph_6 {
  width: 47.04vw;
  height: 5vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.25vw;
}

.image-text_12 {
  width: 5.79vw;
  height: 2.61vw;
  margin: 2.96vw 0 0 23.17vw;
}

.label_1 {
  width: 1.57vw;
  height: 1.57vw;
  margin-top: 0.68vw;
}

.text-group_1 {
  width: 3.7vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.61vw;
}

.box_7 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 17.19vw;
  width: 53.65vw;
  margin: 1.19vw 0 0 23.17vw;
}

.group_3 {
  border-radius: 8px;
  background-image: url(./img/da36ba129190407d9f85de78f5f3456d_mergeImage.png);
  width: 53.65vw;
  height: 17.19vw;
}

.image-text_13 {
  width: 10.47vw;
  height: 1.62vw;
  margin: 1.71vw 0 0 31.4vw;
}

.group_4 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.42vw;
  height: 0.42vw;
  margin-top: 0.58vw;
}

.text-group_2 {
  width: 9.54vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.image-text_14 {
  width: 9.69vw;
  height: 1.62vw;
  margin: 0.83vw 0 0 31.4vw;
}

.block_2 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.42vw;
  height: 0.42vw;
  margin-top: 0.58vw;
}

.text-group_3 {
  width: 8.75vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.image-text_15 {
  width: 12.14vw;
  height: 1.62vw;
  margin: 0.83vw 0 0 31.4vw;
}

.box_8 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.42vw;
  height: 0.42vw;
  margin-top: 0.58vw;
}

.text-group_4 {
  width: 11.2vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.image-text_16 {
  width: 8.86vw;
  height: 1.62vw;
  margin: 0.83vw 0 0 31.4vw;
}

.group_5 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.42vw;
  height: 0.42vw;
  margin-top: 0.58vw;
}

.text-group_5 {
  width: 7.92vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.image-text_17 {
  width: 12.14vw;
  height: 1.62vw;
  margin: 0.83vw 0 0 31.4vw;
}

.box_9 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.42vw;
  height: 0.42vw;
  margin-top: 0.58vw;
}

.text-group_6 {
  width: 11.2vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.image-text_18 {
  width: 11.31vw;
  height: 1.62vw;
  margin: 0.83vw 0 1.61vw 31.4vw;
}

.section_1 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.42vw;
  height: 0.42vw;
  margin-top: 0.58vw;
}

.text-group_7 {
  width: 10.37vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.box_10 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 53.65vw;
  height: 8.81vw;
  margin: 1.04vw 0 0 23.17vw;
}

.text-group_13 {
  width: 48.44vw;
  height: 5.99vw;
  margin: 1.35vw 0 0 2.6vw;
}

.text_28 {
  width: 9.28vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.41vw;
}

.text_29 {
  width: 48.44vw;
  height: 3.75vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.67);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.25vw;
  margin-top: 0.84vw;
}

.image-text_19 {
  width: 5.79vw;
  height: 2.61vw;
  margin: 2.96vw 0 0 23.12vw;
}

.label_2 {
  width: 1.57vw;
  height: 1.57vw;
  margin-top: 0.68vw;
}

.text-group_9 {
  width: 3.7vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.61vw;
}

.box_11 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 10.11vw;
  width: 53.65vw;
  margin: 1.19vw 0 0 23.17vw;
}

.box_12 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  position: relative;
  width: 53.65vw;
  height: 10.11vw;
}

.image-text_20 {
  width: 6.72vw;
  height: 3.86vw;
  margin: 3.17vw 0 0 3.12vw;
}

.group_6 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.42vw;
  height: 0.42vw;
  margin-top: 2.82vw;
}

.text-group_14 {
  width: 5.79vw;
  height: 3.86vw;
}

.text_30 {
  width: 3.29vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.text_31 {
  width: 5.79vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
  margin-top: 0.63vw;
}

.image-text_21 {
  position: absolute;
  left: 3.13vw;
  top: 3.18vw;
  width: 6.72vw;
  height: 3.86vw;
}

.box_13 {
  background-color: rgba(0, 0, 0, 1);
  border-radius: 50%;
  width: 0.42vw;
  height: 0.42vw;
  margin-top: 0.58vw;
}

.text-group_15 {
  width: 5.79vw;
  height: 3.86vw;
}

.text_30 {
  width: 3.29vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.text_31 {
  width: 5.79vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
  margin-top: 0.63vw;
}

.box_14 {
  height: 8.86vw;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
  margin: 3.12vw 0 0 23.17vw;
}

.text-wrapper_16 {
  width: 4.95vw;
  height: 1.72vw;
  margin: 1.35vw 0 0 2.6vw;
}

.text_32 {
  width: 4.95vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.25vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
}

.box_19 {
  width: 44.74vw;
  height: 2.5vw;
  margin: 0.15vw 0 3.12vw 2.6vw;
}

.text_33 {
  width: 33.29vw;
  height: 2.09vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.42vw;
}

.text-wrapper_11 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
}

.text_34 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.box_16 {
  height: 11.98vw;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
  position: relative;
  margin: 3.12vw 0 0 23.17vw;
}

.text-wrapper_17 {
  width: 21.31vw;
  height: 1.15vw;
  margin: 0.93vw 0 0 3.12vw;
}

.text_35 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_36 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_37 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text-wrapper_18 {
  width: 23.08vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_38 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_39 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_40 {
  width: 4.95vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.84vw;
}

.text-wrapper_19 {
  width: 3.29vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_41 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text-wrapper_20 {
  width: 1.67vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_42 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.block_5 {
  width: 9.28vw;
  height: 2.87vw;
  margin: 1.56vw 0 0.15vw 1.04vw;
}

.image-text_22 {
  width: 9.28vw;
  height: 2.87vw;
}

.image_1 {
  width: 2.92vw;
  height: 2.87vw;
}

.text-group_12 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin-top: 0.16vw;
}

.block_4 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 12.19vw;
  top: 8.86vw;
  width: 64.64vw;
  height: 3.13vw;
}
