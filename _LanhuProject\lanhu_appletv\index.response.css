.page {
  position: relative;
  width: 100vw;
  height: 136.15vw;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 100vw;
  height: 136.15vw;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 100vw;
  height: 4.8vw;
}

.text_1 {
  width: 3.6vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 2.81vw 0 0 14.89vw;
}

.image_1 {
  width: 2.92vw;
  height: 2.87vw;
  margin: 0.93vw 0 0 5.46vw;
}

.text_2 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin: 1.09vw 0 0 0.2vw;
}

.text_3 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 15.93vw;
}

.text_4 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.text_5 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.text_6 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.text_7 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.thumbnail_1 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 2.03vw 23.28vw 0 1.66vw;
}

.block_2 {
  position: relative;
  width: 100vw;
  height: 131.41vw;
  margin-bottom: 0.06vw;
}

.block_3 {
  width: 86.57vw;
  height: 4.8vw;
  margin-left: 13.44vw;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(14, 107, 228, 1);
  height: 4.8vw;
  width: 6.15vw;
}

.text_8 {
  width: 2.82vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.group_2 {
  background-color: rgba(9, 9, 9, 1);
  position: relative;
  width: 76.05vw;
  height: 4.8vw;
}

.text_9 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.text_10 {
  width: 2.82vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 2.6vw;
}

.text_11 {
  width: 1.88vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 2.6vw;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 4.8vw;
  width: 6.36vw;
  margin: 0 23.28vw 0 31.14vw;
}

.text_12 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.61vw 0 0 1.35vw;
}

.image_2 {
  position: absolute;
  left: -4.37vw;
  top: 2.35vw;
  width: 4.9vw;
  height: 0.06vw;
}

.block_4 {
  width: 75vw;
  height: 19.9vw;
  margin-left: 12.5vw;
}

.box_1 {
  background-image: url(./img/ddcfe9406c794a5ba905a5b3700a9200_mergeImage.png);
  width: 75vw;
  height: 19.9vw;
  border: 1px solid rgba(151, 151, 151, 1);
}

.text_13 {
  width: 43.65vw;
  height: 3.39vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 2.5vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 3.39vw;
  margin: 4.79vw 0 0 15.2vw;
}

.text-wrapper_3 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
  margin: 2.86vw 0 0 34.89vw;
}

.text_14 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.image_3 {
  width: 6.67vw;
  height: 0.73vw;
  margin: 4.73vw 0 0.98vw 34.16vw;
}

.text-wrapper_4 {
  width: 7.4vw;
  height: 2.61vw;
  margin: 2.96vw 0 0 26.56vw;
}

.text_15 {
  width: 7.4vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.61vw;
}

.block_5 {
  width: 55.89vw;
  height: 13.08vw;
  margin: 1.19vw 0 0 12.7vw;
}

.group_3 {
  width: 5.21vw;
  height: 4.38vw;
  margin-top: 5.42vw;
}

.text_16 {
  width: 3.6vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin-left: 0.73vw;
}

.text-wrapper_5 {
  background-color: rgba(14, 107, 228, 1);
  height: 2.4vw;
  margin-top: 0.94vw;
  width: 5.21vw;
}

.text_17 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.93vw;
}

.group_4 {
  background-color: rgba(244, 244, 244, 1);
  position: relative;
  width: 14.64vw;
  height: 13.08vw;
  margin-left: 4.64vw;
}

.text-group_1 {
  width: 4.95vw;
  height: 3.18vw;
  margin: 1.45vw 0 0 1.04vw;
}

.text_18 {
  width: 4.33vw;
  height: 1.52vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.09vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.52vw;
}

.text_19 {
  width: 4.95vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-top: 0.53vw;
}

.text-wrapper_6 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
  margin: 2.7vw 0 3.33vw 1.04vw;
}

.text_20 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.image_4 {
  position: absolute;
  left: -4.06vw;
  top: 8.55vw;
  width: 4.9vw;
  height: 0.06vw;
}

.image-text_1 {
  width: 5.16vw;
  height: 1.2vw;
  margin: 11.09vw 0 0 0.52vw;
}

.image_5 {
  width: 1.57vw;
  height: 0.99vw;
  margin-top: 0.21vw;
}

.text-group_2 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.group_5 {
  background-color: rgba(216, 216, 216, 0.3);
  width: 14.64vw;
  height: 13.08vw;
  margin-left: 6.72vw;
}

.text-group_3 {
  width: 10.73vw;
  height: 5.32vw;
  margin: 1.45vw 0 0 1.04vw;
}

.text_21 {
  width: 3.23vw;
  height: 1.52vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.09vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.52vw;
}

.text_22 {
  width: 10.73vw;
  height: 3.13vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.68vw;
}

.text-wrapper_7 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
  margin: 0.57vw 0 3.33vw 1.04vw;
}

.text_23 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.image-text_2 {
  width: 3.86vw;
  height: 1.2vw;
  margin: 11.3vw 0 0 0.52vw;
}

.thumbnail_2 {
  width: 1.05vw;
  height: 1.05vw;
  margin-top: 0.16vw;
}

.text-group_4 {
  width: 2.5vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.block_6 {
  width: 63.34vw;
  height: 13.08vw;
  margin: 2.08vw 0 0 6.04vw;
}

.box_2 {
  width: 4.07vw;
  height: 4.74vw;
  margin-top: 2.71vw;
}

.text-wrapper_8 {
  background-color: rgba(216, 216, 216, 1);
  height: 1.78vw;
  border: 1px solid rgba(151, 151, 151, 1);
  width: 4.07vw;
}

.text_24 {
  width: 2.19vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.31vw 0 0 0.93vw;
}

.text-wrapper_9 {
  background-color: rgba(216, 216, 216, 1);
  height: 1.78vw;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 1.2vw;
  width: 4.07vw;
}

.text_25 {
  width: 2.19vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.31vw 0 0 0.93vw;
}

.box_3 {
  background-color: rgba(244, 244, 244, 1);
  width: 14.64vw;
  height: 13.08vw;
  margin-left: 12.45vw;
}

.text-group_5 {
  width: 11.36vw;
  height: 5.27vw;
  margin: 1.45vw 0 0 1.04vw;
}

.text_26 {
  width: 2.19vw;
  height: 1.52vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.09vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.52vw;
}

.text_27 {
  width: 11.36vw;
  height: 3.13vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.63vw;
}

.text-wrapper_10 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
  margin: 1.4vw 0 2.55vw 1.04vw;
}

.text_28 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.image-text_3 {
  width: 4.54vw;
  height: 1.2vw;
  margin: 11.09vw 0 0 1.14vw;
}

.thumbnail_3 {
  width: 0.94vw;
  height: 1.05vw;
  margin-top: 0.16vw;
}

.text-group_6 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.box_4 {
  background-color: rgba(216, 216, 216, 0.3);
  width: 14.64vw;
  height: 13.08vw;
  margin-left: 6.72vw;
}

.text-group_7 {
  width: 10.84vw;
  height: 6.36vw;
  margin: 1.45vw 0 0 1.04vw;
}

.text_29 {
  width: 4.33vw;
  height: 1.52vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.09vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.52vw;
}

.text_30 {
  width: 10.84vw;
  height: 4.17vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.68vw;
}

.text-wrapper_11 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
  margin: 0.31vw 0 2.55vw 1.04vw;
}

.text_31 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.image-text_4 {
  width: 4.64vw;
  height: 1.2vw;
  margin: 11.3vw 0 0 0.52vw;
}

.thumbnail_4 {
  width: 1.05vw;
  height: 1.05vw;
  margin-top: 0.16vw;
}

.text-group_8 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.block_7 {
  width: 45.37vw;
  height: 6.2vw;
  margin: 3.48vw 0 0 26.71vw;
}

.text-group_9 {
  width: 45.37vw;
  height: 6.2vw;
}

.text_32 {
  width: 18.49vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.61vw;
  margin-left: 14.07vw;
}

.text_33 {
  width: 45.37vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 1.41vw;
  margin-top: 0.79vw;
}

.block_8 {
  width: 53.65vw;
  height: 6.25vw;
  margin: 2.91vw 0 0 22.55vw;
}

.section_1 {
  background-color: rgba(0, 0, 0, 1);
  width: 53.65vw;
  height: 6.25vw;
}

.text_34 {
  width: 5vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.25vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
  margin: 2.23vw 0 0 10.72vw;
}

.text-wrapper_12 {
  background-image: url(./img/583d9fa2bf8848e38fcfc6894cd7d9c1_mergeImage.png);
  height: 6.25vw;
  margin-left: 11.31vw;
  width: 26.62vw;
}

.text_35 {
  width: 5vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.25vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
  margin: 2.23vw 0 0 10.83vw;
}

.block_9 {
  width: 59.38vw;
  height: 2.87vw;
  margin: 2.7vw 0 0 26.56vw;
}

.text_36 {
  width: 7.4vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.61vw;
  margin-top: 0.27vw;
}

.image-text_5 {
  width: 4.17vw;
  height: 1.05vw;
  margin: 1.4vw 0 0 37.76vw;
}

.text-group_10 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(118, 118, 118, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_5 {
  width: 0.73vw;
  height: 0.73vw;
  margin-top: 0.21vw;
}

.image_6 {
  width: 4.02vw;
  height: 0.06vw;
  margin: 1.92vw 0 0 1.04vw;
}

.box_5 {
  width: 4.17vw;
  height: 2.45vw;
  margin-left: 0.84vw;
}

.text_37 {
  width: 3.6vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.image-text_6 {
  width: 4.17vw;
  height: 1.05vw;
  margin-top: 0.37vw;
}

.text-group_11 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 58, 133, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_6 {
  width: 0.73vw;
  height: 0.73vw;
  margin-top: 0.21vw;
}

.block_10 {
  width: 53.65vw;
  height: 17.61vw;
  margin: 1.19vw 0 0 22.55vw;
}

.list_1 {
  width: 53.65vw;
  height: 17.61vw;
  justify-content: space-between;
}

.image-text_7-0 {
  background-color: rgba(244, 244, 244, 1);
  width: 13.03vw;
  height: 17.61vw;
  margin-right: 0.53vw;
}

.group_6-0 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 13.03vw;
  height: 6.67vw;
  background: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
}

.text-group_12-0 {
  width: 10.84vw;
  height: 5.47vw;
  margin: 0.52vw 0 4.94vw 1.04vw;
}

.text_38-0 {
  width: 10.84vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.41vw;
}

.text_39-0 {
  width: 10.84vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.15vw;
  margin-top: 0.37vw;
}

.image-text_7-1 {
  background-color: rgba(244, 244, 244, 1);
  width: 13.03vw;
  height: 17.61vw;
  margin-right: 0.53vw;
}

.group_6-1 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 13.03vw;
  height: 6.67vw;
  background: url(./img/48c86bd3e7d74ef4a7d049ec56123b91_mergeImage.png);
}

.text-group_12-1 {
  width: 10.84vw;
  height: 5.47vw;
  margin: 0.52vw 0 4.94vw 1.04vw;
}

.text_38-1 {
  width: 10.84vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.41vw;
}

.text_39-1 {
  width: 10.84vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.15vw;
  margin-top: 0.37vw;
}

.image-text_7-2 {
  background-color: rgba(244, 244, 244, 1);
  width: 13.03vw;
  height: 17.61vw;
  margin-right: 0.53vw;
}

.group_6-2 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 13.03vw;
  height: 6.67vw;
  background: url(./img/dba985fbe79b4349ae38dd05d22c4ce0_mergeImage.png);
}

.text-group_12-2 {
  width: 10.84vw;
  height: 5.47vw;
  margin: 0.52vw 0 4.94vw 1.04vw;
}

.text_38-2 {
  width: 10.84vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.41vw;
}

.text_39-2 {
  width: 10.84vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.15vw;
  margin-top: 0.37vw;
}

.image-text_7-3 {
  background-color: rgba(244, 244, 244, 1);
  width: 13.03vw;
  height: 17.61vw;
  margin-right: 0.53vw;
}

.group_6-3 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 13.03vw;
  height: 6.67vw;
  background: url(./img/1f5b9ade7e9240f0bcbb8fcaec428af7_mergeImage.png);
}

.text-group_12-3 {
  width: 10.84vw;
  height: 5.47vw;
  margin: 0.52vw 0 4.94vw 1.04vw;
}

.text_38-3 {
  width: 10.84vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.41vw;
}

.text_39-3 {
  width: 10.84vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.15vw;
  margin-top: 0.37vw;
}

.block_11 {
  width: 53.65vw;
  height: 8.86vw;
  margin: 3.12vw 0 0 22.55vw;
}

.box_6 {
  height: 8.86vw;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
}

.text-wrapper_13 {
  width: 4.95vw;
  height: 1.72vw;
  margin: 1.35vw 0 0 2.6vw;
}

.text_40 {
  width: 4.95vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.25vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
}

.box_7 {
  width: 44.74vw;
  height: 2.5vw;
  margin: 0.15vw 0 3.12vw 2.6vw;
}

.text_41 {
  width: 33.29vw;
  height: 2.09vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.42vw;
}

.text-wrapper_14 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
}

.text_42 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.block_12 {
  width: 53.65vw;
  height: 11.98vw;
  margin: 4.47vw 0 0.05vw 22.55vw;
}

.section_2 {
  height: 11.98vw;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
  position: relative;
}

.text-wrapper_15 {
  width: 21.31vw;
  height: 1.15vw;
  margin: 0.93vw 0 0 3.12vw;
}

.text_43 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_44 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_45 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text-wrapper_16 {
  width: 23.08vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_46 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_47 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_48 {
  width: 4.95vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.84vw;
}

.text-wrapper_17 {
  width: 3.29vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_49 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text-wrapper_18 {
  width: 1.67vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_50 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.group_7 {
  width: 9.28vw;
  height: 2.87vw;
  margin: 1.56vw 0 0.15vw 1.04vw;
}

.image-text_8 {
  width: 9.28vw;
  height: 2.87vw;
}

.image_7 {
  width: 2.92vw;
  height: 2.87vw;
}

.text-group_13 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin-top: 0.16vw;
}

.group_8 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 12.82vw;
  top: 8.86vw;
  width: 64.64vw;
  height: 3.13vw;
}

.block_13 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 8.65vw;
  width: 14.07vw;
  position: absolute;
  left: 35vw;
  top: 32.19vw;
}

.box_8 {
  background-image: url(./img/2e3a71974cfc4afc8c36dc3a32bc9d95_mergeImage.png);
  width: 14.07vw;
  height: 8.65vw;
}

.block_14 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 8.65vw;
  width: 14.07vw;
  position: absolute;
  left: 62.19vw;
  top: 32.04vw;
}

.group_9 {
  background-image: url(./img/1b68b0c36f9e41caa0ec479a3ca737db_mergeImage.png);
  width: 14.07vw;
  height: 8.65vw;
}

.block_15 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 8.65vw;
  width: 14.07vw;
  position: absolute;
  left: 35vw;
  top: 47.35vw;
}

.box_9 {
  background-image: url(./img/8a237950cd5a4340a3dc10bcb2cf5975_mergeImage.png);
  width: 14.07vw;
  height: 8.65vw;
}

.block_16 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 8.65vw;
  width: 14.07vw;
  position: absolute;
  left: 62.19vw;
  top: 47.19vw;
}

.group_10 {
  background-image: url(./img/2a5b1018e10441e6aae6f6edb879113e_mergeImage.png);
  width: 14.07vw;
  height: 8.65vw;
}
