<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图演示 - 山西智诚</title>
    <link rel="stylesheet" type="text/css" href="carousel.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'AlibabaPuHuiTi-Regular', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .demo-title {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .demo-container {
            max-width: 1440px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .demo-info {
            padding: 2rem;
            background-color: #fff;
            border-top: 1px solid #e9ecef;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 1rem;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #ec2914;
        }
        
        .info-card h3 {
            color: #ec2914;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
        
        .info-card p {
            color: #6c757d;
            font-size: 0.95rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        @media (max-width: 768px) {
            .demo-header {
                padding: 1.5rem 1rem;
            }
            
            .demo-title {
                font-size: 2rem;
            }
            
            .demo-info {
                padding: 1.5rem;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="demo-header">
        <h1 class="demo-title">轮播图组件演示</h1>
        <p class="demo-subtitle">基于jQuery的响应式轮播图，支持指示点导航和自动播放</p>
    </header>

    <div class="demo-container">
        <!-- 轮播图容器 -->
        <div id="carousel-container"></div>
        
        <div class="demo-info">
            <h2 style="color: #333; margin-bottom: 1rem;">功能特性</h2>
            <div class="info-grid">
                <div class="info-card">
                    <h3>自动轮播</h3>
                    <p>每4秒自动切换到下一张图片，鼠标悬停时暂停播放，离开后继续播放。</p>
                </div>
                
                <div class="info-card">
                    <h3>指示点导航</h3>
                    <p>底部圆点指示器，点击可直接跳转到对应的幻灯片，当前激活状态有明显视觉反馈。</p>
                </div>
                
                <div class="info-card">
                    <h3>平滑过渡</h3>
                    <p>使用CSS3过渡效果，提供流畅的淡入淡出动画效果。</p>
                </div>
                
                <div class="info-card">
                    <h3>响应式设计</h3>
                    <p>适配不同屏幕尺寸，在移动设备上自动调整布局和字体大小。</p>
                </div>
            </div>
            
            <div style="margin-top: 2rem;">
                <h3 style="color: #333; margin-bottom: 1rem;">技术实现</h3>
                <ul class="feature-list">
                    <li>使用jQuery简化DOM操作和事件处理</li>
                    <li>CSS3过渡动画提供流畅的视觉效果</li>
                    <li>组件化设计，易于集成和维护</li>
                    <li>支持页面可见性API，页面隐藏时暂停轮播</li>
                    <li>无障碍设计，支持键盘导航</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="../../assets/jquery-3.7.1.min.js"></script>
    <script>
        // 直接在页面中嵌入轮播图HTML，避免fetch请求
        $(document).ready(function() {
            const carouselHTML = `
                <section class="carousel-container">
                    <div class="carousel-wrapper">
                        <div class="carousel-slides">
                            <div class="carousel-slide active" data-bg="../../home/<USER>/aedda77a45764f4a9a84182ecfa9e1d7_mergeImage.png">
                                <div class="slide-content">
                                    <h2 class="hero-title">我们推动积极变革，让您的业务大有不同</h2>
                                    <button class="cta-button">了解更多</button>
                                </div>
                            </div>
                            <div class="carousel-slide" data-bg="../../home/<USER>/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png">
                                <div class="slide-content">
                                    <h2 class="hero-title">专业服务，值得信赖的合作伙伴</h2>
                                    <button class="cta-button">立即咨询</button>
                                </div>
                            </div>
                            <div class="carousel-slide" data-bg="../../home/<USER>/d95c1a527e584dbd8ba4a677be6c22d9_mergeImage.png">
                                <div class="slide-content">
                                    <h2 class="hero-title">创新解决方案，助力企业发展</h2>
                                    <button class="cta-button">查看案例</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="carousel-indicators">
                            <span class="indicator active" data-slide="0"></span>
                            <span class="indicator" data-slide="1"></span>
                            <span class="indicator" data-slide="2"></span>
                        </div>
                    </div>
                </section>
            `;
            
            $('#carousel-container').html(carouselHTML);
            
            // 初始化轮播图功能
            initCarousel();
        });
        
        function initCarousel() {
            let currentSlide = 0;
            let slideInterval;
            const $slides = $('.carousel-slide');
            const $indicators = $('.indicator');
            const $carouselContainer = $('.carousel-container');
            
            if ($slides.length === 0) return;

            // 设置背景图片
            $slides.each(function() {
                const bgImage = $(this).data('bg');
                if (bgImage) {
                    $(this).css('background-image', \`url(\${bgImage})\`);
                }
            });

            // 绑定指示点点击事件
            $indicators.on('click', function() {
                const index = $(this).data('slide');
                goToSlide(index);
            });

            // 切换到指定幻灯片
            function goToSlide(index) {
                if (index < 0 || index >= $slides.length) return;
                
                $slides.eq(currentSlide).removeClass('active');
                $indicators.eq(currentSlide).removeClass('active');
                
                currentSlide = index;
                $slides.eq(currentSlide).addClass('active');
                $indicators.eq(currentSlide).addClass('active');
            }

            // 下一张幻灯片
            function nextSlide() {
                const nextIndex = (currentSlide + 1) % $slides.length;
                goToSlide(nextIndex);
            }

            // 开始自动轮播
            function startAutoSlide() {
                stopAutoSlide();
                slideInterval = setInterval(nextSlide, 4000);
            }

            // 停止自动轮播
            function stopAutoSlide() {
                if (slideInterval) {
                    clearInterval(slideInterval);
                    slideInterval = null;
                }
            }

            // 鼠标悬停时暂停轮播
            $carouselContainer.on('mouseenter', stopAutoSlide);
            $carouselContainer.on('mouseleave', startAutoSlide);

            // 页面隐藏时停止轮播，显示时恢复
            $(document).on('visibilitychange', function() {
                if (document.hidden) {
                    stopAutoSlide();
                } else {
                    startAutoSlide();
                }
            });

            // 开始自动轮播
            startAutoSlide();
        }
    </script>
</body>
</html>
