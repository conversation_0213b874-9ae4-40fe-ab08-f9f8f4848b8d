// 联系我们组件加载器
(function () {
	// 获取组件路径
	function getComponentPath() {
		const currentPath = window.location.pathname;
		// 根据当前路径确定组件的相对路径
		if (currentPath.includes("/home/")) {
			return "../component/contact/contact.html";
		} else if (currentPath.includes("/xiangqingye/")) {
			return "../component/contact/contact.html";
		} else if (currentPath.includes("/guanyuwomen/")) {
			return "../component/contact/contact.html";
		} else {
			// 默认路径
			return "../component/contact/contact.html";
		}
	}

	// 加载联系我们组件
	function loadContact() {
		const componentPath = getComponentPath();
		fetch(componentPath)
			.then((response) => response.text())
			.then((html) => {
				const contactContainer = document.getElementById("contact-container");
				if (contactContainer) {
					contactContainer.innerHTML = html;
				}
			})
			.catch((error) => {
				console.error("联系我们组件加载失败:", error);
			});
	}

	// 页面加载完成后执行
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", loadContact);
	} else {
		loadContact();
	}
})();
