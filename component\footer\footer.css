/* 页脚 */
.footer {
	background: #000;
	background-size: 100% 100%;
	height: 170px;
	width: 1030px;
	margin: 86px auto 0;
	padding: 18px 60px 3px;
	position: relative;
	display: flex;
	flex-direction: column;
	gap: 30px;
}

.footer-links {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 110px;
	margin-bottom: 12px;
}

.footer-column {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.footer-column-title {
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	color: #ffffff;
	line-height: 22px;
	margin: 0;
}

.footer-link {
	font-size: 16px;
	margin-left: 10px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #ffffff;
	text-decoration: none;
	line-height: 22px;
	transition: color 0.3s ease;
}

.footer-link:hover {
	color: #0055c3;
}

/* Footer品牌区域 - 三部分布局 */
.footer-brand {
	display: flex;
	align-items: center;
	width: 100%;
	height: 60px;
}

.footer-brand-left {
	flex: 1;
	background-color: transparent;
	height: 100%;
}

.footer-brand-right {
	flex: 1;
	background-color: #000;
	height: 100%;
}

.footer-brand-center {
	display: flex;
	align-items: center;
	width: 1030px;
	height: 100%;
	background-color: #ffffff;
}

.footer-logo {
	display: flex;
	align-items: center;
	gap: 12px;
	width: 246px;
	padding-left: 20px;
}

.footer-logo-image {
	width: 56px;
	height: 55px;
}

.footer-company-name {
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	color: #000000;
}

.footer-brand-fill {
	flex: 1;
	background-color: #000000;
	height: 100%;
}

.footer-bottom {
	background-color: #000000;
	position: absolute;
	left: 246px;
	top: 170px;
	width: 1241px;
	height: 60px;
}
