// 页脚组件加载器
(function () {
	// 加载页脚组件
	function loadFooter() {
		fetch("../component/footer/footer.html")
			.then((response) => response.text())
			.then((html) => {
				const footerContainer = document.getElementById("footer-container");
				if (footerContainer) {
					footerContainer.innerHTML = html;
				}
			})
			.catch((error) => {
				console.error("页脚组件加载失败:", error);
			});
	}

	// 页面加载完成后执行
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", loadFooter);
	} else {
		loadFooter();
	}
})();
