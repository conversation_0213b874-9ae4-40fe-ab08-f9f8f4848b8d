.page {
  position: relative;
  width: 1920px;
  height: 2614px;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 1920px;
  height: 2614px;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 1920px;
  height: 92px;
}

.text_1 {
  width: 69px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 54px 0 0 286px;
}

.image_1 {
  width: 56px;
  height: 55px;
  margin: 18px 0 0 105px;
}

.text_2 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin: 21px 0 0 4px;
}

.text_3 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 306px;
}

.text_4 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_5 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_6 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_7 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.thumbnail_1 {
  width: 14px;
  height: 14px;
  margin: 39px 447px 0 32px;
}

.block_2 {
  position: relative;
  width: 1920px;
  height: 2523px;
  margin-bottom: 1px;
}

.block_3 {
  width: 1662px;
  height: 92px;
  margin-left: 258px;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(14, 107, 228, 1);
  height: 92px;
  width: 118px;
}

.text_8 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.group_2 {
  background-color: rgba(9, 9, 9, 1);
  position: relative;
  width: 1460px;
  height: 92px;
}

.text_9 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_10 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 50px;
}

.text_11 {
  width: 36px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 50px;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 122px;
  margin: 0 447px 0 598px;
}

.text_12 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 31px 0 0 26px;
}

.image_2 {
  position: absolute;
  left: -84px;
  top: 45px;
  width: 94px;
  height: 1px;
}

.block_4 {
  width: 1440px;
  height: 382px;
  margin-left: 240px;
}

.box_1 {
  background-image: url(./img/ddcfe9406c794a5ba905a5b3700a9200_mergeImage.png);
  width: 1440px;
  height: 382px;
  border: 1px solid rgba(151, 151, 151, 1);
}

.text_13 {
  width: 838px;
  height: 65px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 48px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 65px;
  margin: 92px 0 0 292px;
}

.text-wrapper_3 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
  margin: 55px 0 0 670px;
}

.text_14 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.image_3 {
  width: 128px;
  height: 14px;
  margin: 91px 0 19px 656px;
}

.text-wrapper_4 {
  width: 142px;
  height: 50px;
  margin: 57px 0 0 510px;
}

.text_15 {
  width: 142px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
}

.block_5 {
  width: 1073px;
  height: 251px;
  margin: 23px 0 0 244px;
}

.group_3 {
  width: 100px;
  height: 84px;
  margin-top: 104px;
}

.text_16 {
  width: 69px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 14px;
}

.text-wrapper_5 {
  background-color: rgba(14, 107, 228, 1);
  height: 46px;
  margin-top: 18px;
  width: 100px;
}

.text_17 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 18px;
}

.group_4 {
  background-color: rgba(244, 244, 244, 1);
  position: relative;
  width: 281px;
  height: 251px;
  margin-left: 89px;
}

.text-group_1 {
  width: 95px;
  height: 61px;
  margin: 28px 0 0 20px;
}

.text_18 {
  width: 83px;
  height: 29px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 21px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 29px;
}

.text_19 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-top: 10px;
}

.text-wrapper_6 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
  margin: 52px 0 64px 20px;
}

.text_20 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.image_4 {
  position: absolute;
  left: -78px;
  top: 164px;
  width: 94px;
  height: 1px;
}

.image-text_1 {
  width: 99px;
  height: 23px;
  margin: 213px 0 0 10px;
}

.image_5 {
  width: 30px;
  height: 19px;
  margin-top: 4px;
}

.text-group_2 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_5 {
  background-color: rgba(216, 216, 216, 0.3);
  width: 281px;
  height: 251px;
  margin-left: 129px;
}

.text-group_3 {
  width: 206px;
  height: 102px;
  margin: 28px 0 0 20px;
}

.text_21 {
  width: 62px;
  height: 29px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 21px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 29px;
}

.text_22 {
  width: 206px;
  height: 60px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 20px;
  margin-top: 13px;
}

.text-wrapper_7 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
  margin: 11px 0 64px 20px;
}

.text_23 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.image-text_2 {
  width: 74px;
  height: 23px;
  margin: 217px 0 0 10px;
}

.thumbnail_2 {
  width: 20px;
  height: 20px;
  margin-top: 3px;
}

.text-group_4 {
  width: 48px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.block_6 {
  width: 1216px;
  height: 251px;
  margin: 40px 0 0 116px;
}

.box_2 {
  width: 78px;
  height: 91px;
  margin-top: 52px;
}

.text-wrapper_8 {
  background-color: rgba(216, 216, 216, 1);
  height: 34px;
  border: 1px solid rgba(151, 151, 151, 1);
  width: 78px;
}

.text_24 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.text-wrapper_9 {
  background-color: rgba(216, 216, 216, 1);
  height: 34px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 23px;
  width: 78px;
}

.text_25 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.box_3 {
  background-color: rgba(244, 244, 244, 1);
  width: 281px;
  height: 251px;
  margin-left: 239px;
}

.text-group_5 {
  width: 218px;
  height: 101px;
  margin: 28px 0 0 20px;
}

.text_26 {
  width: 42px;
  height: 29px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 21px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 29px;
}

.text_27 {
  width: 218px;
  height: 60px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 20px;
  margin-top: 12px;
}

.text-wrapper_10 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
  margin: 27px 0 49px 20px;
}

.text_28 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.image-text_3 {
  width: 87px;
  height: 23px;
  margin: 213px 0 0 22px;
}

.thumbnail_3 {
  width: 18px;
  height: 20px;
  margin-top: 3px;
}

.text-group_6 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.box_4 {
  background-color: rgba(216, 216, 216, 0.3);
  width: 281px;
  height: 251px;
  margin-left: 129px;
}

.text-group_7 {
  width: 208px;
  height: 122px;
  margin: 28px 0 0 20px;
}

.text_29 {
  width: 83px;
  height: 29px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 21px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 29px;
}

.text_30 {
  width: 208px;
  height: 80px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 20px;
  margin-top: 13px;
}

.text-wrapper_11 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
  margin: 6px 0 49px 20px;
}

.text_31 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.image-text_4 {
  width: 89px;
  height: 23px;
  margin: 217px 0 0 10px;
}

.thumbnail_4 {
  width: 20px;
  height: 20px;
  margin-top: 3px;
}

.text-group_8 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.block_7 {
  width: 871px;
  height: 119px;
  margin: 67px 0 0 513px;
}

.text-group_9 {
  width: 871px;
  height: 119px;
}

.text_32 {
  width: 355px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
  margin-left: 270px;
}

.text_33 {
  width: 871px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 27px;
  margin-top: 15px;
}

.block_8 {
  width: 1030px;
  height: 120px;
  margin: 56px 0 0 433px;
}

.section_1 {
  background-color: rgba(0, 0, 0, 1);
  width: 1030px;
  height: 120px;
}

.text_34 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 43px 0 0 206px;
}

.text-wrapper_12 {
  background-image: url(./img/583d9fa2bf8848e38fcfc6894cd7d9c1_mergeImage.png);
  height: 120px;
  margin-left: 217px;
  width: 511px;
}

.text_35 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 43px 0 0 208px;
}

.block_9 {
  width: 1140px;
  height: 55px;
  margin: 52px 0 0 510px;
}

.text_36 {
  width: 142px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
  margin-top: 5px;
}

.image-text_5 {
  width: 80px;
  height: 20px;
  margin: 27px 0 0 725px;
}

.text-group_10 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(118, 118, 118, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_5 {
  width: 14px;
  height: 14px;
  margin-top: 4px;
}

.image_6 {
  width: 77px;
  height: 1px;
  margin: 37px 0 0 20px;
}

.box_5 {
  width: 80px;
  height: 47px;
  margin-left: 16px;
}

.text_37 {
  width: 69px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.image-text_6 {
  width: 80px;
  height: 20px;
  margin-top: 7px;
}

.text-group_11 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 58, 133, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_6 {
  width: 14px;
  height: 14px;
  margin-top: 4px;
}

.block_10 {
  width: 1030px;
  height: 338px;
  margin: 23px 0 0 433px;
}

.list_1 {
  width: 1030px;
  height: 338px;
  justify-content: space-between;
}

.image-text_7-0 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_6-0 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
}

.text-group_12-0 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_38-0 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 27px;
}

.text_39-0 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_7-1 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_6-1 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(./img/48c86bd3e7d74ef4a7d049ec56123b91_mergeImage.png);
}

.text-group_12-1 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_38-1 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 27px;
}

.text_39-1 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_7-2 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_6-2 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(./img/dba985fbe79b4349ae38dd05d22c4ce0_mergeImage.png);
}

.text-group_12-2 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_38-2 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 27px;
}

.text_39-2 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_7-3 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_6-3 {
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(./img/1f5b9ade7e9240f0bcbb8fcaec428af7_mergeImage.png);
}

.text-group_12-3 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_38-3 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 27px;
}

.text_39-3 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.block_11 {
  width: 1030px;
  height: 170px;
  margin: 60px 0 0 433px;
}

.box_6 {
  height: 170px;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
}

.text-wrapper_13 {
  width: 95px;
  height: 33px;
  margin: 26px 0 0 50px;
}

.text_40 {
  width: 95px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.box_7 {
  width: 859px;
  height: 48px;
  margin: 3px 0 60px 50px;
}

.text_41 {
  width: 639px;
  height: 40px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
  margin-top: 8px;
}

.text-wrapper_14 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
}

.text_42 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.block_12 {
  width: 1030px;
  height: 230px;
  margin: 86px 0 1px 433px;
}

.section_2 {
  height: 230px;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  position: relative;
}

.text-wrapper_15 {
  width: 409px;
  height: 22px;
  margin: 18px 0 0 60px;
}

.text_43 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_44 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_45 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text-wrapper_16 {
  width: 443px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_46 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_47 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_48 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 112px;
}

.text-wrapper_17 {
  width: 63px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_49 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text-wrapper_18 {
  width: 32px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_50 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_7 {
  width: 178px;
  height: 55px;
  margin: 30px 0 3px 20px;
}

.image-text_8 {
  width: 178px;
  height: 55px;
}

.image_7 {
  width: 56px;
  height: 55px;
}

.text-group_13 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin-top: 3px;
}

.group_8 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 246px;
  top: 170px;
  width: 1241px;
  height: 60px;
}

.block_13 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 166px;
  width: 270px;
  position: absolute;
  left: 672px;
  top: 618px;
}

.box_8 {
  background-image: url(./img/2e3a71974cfc4afc8c36dc3a32bc9d95_mergeImage.png);
  width: 270px;
  height: 166px;
}

.block_14 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 166px;
  width: 270px;
  position: absolute;
  left: 1194px;
  top: 615px;
}

.group_9 {
  background-image: url(./img/1b68b0c36f9e41caa0ec479a3ca737db_mergeImage.png);
  width: 270px;
  height: 166px;
}

.block_15 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 166px;
  width: 270px;
  position: absolute;
  left: 672px;
  top: 909px;
}

.box_9 {
  background-image: url(./img/8a237950cd5a4340a3dc10bcb2cf5975_mergeImage.png);
  width: 270px;
  height: 166px;
}

.block_16 {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  height: 166px;
  width: 270px;
  position: absolute;
  left: 1194px;
  top: 906px;
}

.group_10 {
  background-image: url(./img/2a5b1018e10441e6aae6f6edb879113e_mergeImage.png);
  width: 270px;
  height: 166px;
}
